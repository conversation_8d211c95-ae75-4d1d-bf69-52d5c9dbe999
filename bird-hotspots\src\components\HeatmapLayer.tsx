import { useEffect, useRef } from 'react';
import L from 'leaflet';
import { useAppStore } from '../store/useAppStore';

// Import heat layer plugin
import 'leaflet.heat';

interface HeatmapLayerProps {
  map: L.Map | null;
}

const HeatmapLayer = ({ map }: HeatmapLayerProps) => {
  const layerGroupsRef = useRef<{ [key: string]: L.LayerGroup }>({});
  const { species, observations, searchQuery } = useAppStore();

  useEffect(() => {
    if (!map) return;

    // Clear existing layer groups
    Object.values(layerGroupsRef.current).forEach(layerGroup => {
      if (map.hasLayer(layerGroup)) {
        map.removeLayer(layerGroup);
      }
    });
    layerGroupsRef.current = {};

    // Filter species based on search query
    const filteredSpecies = species.filter(speciesItem => {
      if (!speciesItem.isVisible) return false;

      // If there's a search query, filter by species name or scientific name
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        return speciesItem.name.toLowerCase().includes(query) ||
               speciesItem.scientificName.toLowerCase().includes(query);
      }

      return true;
    });

    // Create layer groups for each filtered species
    filteredSpecies.forEach(speciesItem => {

      // Filter observations for this species
      const speciesObservations = observations.filter(
        obs => obs.speciesId === speciesItem.id
      );

      if (speciesObservations.length === 0) return;

      // Create layer group for this species
      const layerGroup = L.layerGroup();

      // Add circle markers for each observation
      speciesObservations.forEach(obs => {
        const circle = L.circleMarker([obs.lat, obs.lng], {
          radius: Math.max(3, obs.intensity * 15),
          fillColor: speciesItem.color,
          color: speciesItem.color,
          weight: 1,
          opacity: 0.8,
          fillOpacity: Math.max(0.3, obs.intensity * 0.7)
        });

        // Add popup with observation details
        circle.bindPopup(`
          <div class="text-sm">
            <strong>${speciesItem.name}</strong><br/>
            <em>${speciesItem.scientificName}</em><br/>
            观察数量: ${obs.count}
          </div>
        `);

        layerGroup.addLayer(circle);
      });

      // Add to map and store reference
      layerGroup.addTo(map);
      layerGroupsRef.current[speciesItem.id] = layerGroup;
    });

    // Cleanup function
    return () => {
      Object.values(layerGroupsRef.current).forEach(layerGroup => {
        if (map.hasLayer(layerGroup)) {
          map.removeLayer(layerGroup);
        }
      });
    };
  }, [map, species, observations, searchQuery]);

  return null; // This component doesn't render anything
};

export default HeatmapLayer;
